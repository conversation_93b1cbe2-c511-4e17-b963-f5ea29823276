"use client";
import React, { useState, useEffect, useCallback, Suspense } from "react";
import { useSearchParams, useRouter } from "next/navigation";
import { motion } from "framer-motion";
import { getProducts } from "@/apis/products";
import ProductCard from "@/components/ProductCard";
import {
  Filter,
  SlidersHorizontal,
  X,
  Check,
  RefreshCw,
  ArrowUpDown,
  Percent,
  CheckCircle,
} from "lucide-react";
import { Button } from "@/components/ui/button";
import SearchField from "@/components/SearchField";
import useDebounce from "@/hooks/useDebounce";

// Define filter state interface
interface FilterState {
  category: string;
  minPrice: number;
  maxPrice: number;
  available: boolean | null;
  discount: boolean | null;
  sort: string;
}

// Loading component for Products page
const ProductsLoading = () => {
  return (
    <div className="container mx-auto px-4 py-16 flex justify-center items-center">
      <div className="animate-pulse">Loading products...</div>
    </div>
  );
};

const Products = () => {
  return (
    <Suspense fallback={<ProductsLoading />}>
      <ProductsContent />
    </Suspense>
  );
};

const ProductsContent = () => {
  const router = useRouter();
  const searchParams = useSearchParams();
  const categoryParam = searchParams.get("category");
  const queryParam = searchParams.get("query");
  const minPriceParam = searchParams.get("minPrice");
  const maxPriceParam = searchParams.get("maxPrice");
  const availableParam = searchParams.get("available");
  const discountParam = searchParams.get("discount");
  const sortParam = searchParams.get("sort");

  // State for products and filtering
  const [products, setProducts] = useState<ProductData[]>([]);
  const [filteredProducts, setFilteredProducts] = useState<ProductData[]>([]);
  const [categories, setCategories] = useState<string[]>([]);
  const [selectedCategory, setSelectedCategory] = useState<string>(
    categoryParam || "all"
  );
  const [searchQuery, setSearchQuery] = useState<string>(queryParam || "");
  const [showFilters, setShowFilters] = useState(false);
  const [isLoading, setIsLoading] = useState(true);

  // Price range state with min and max values from all products
  const [priceRange, setPriceRange] = useState<[number, number]>([
    minPriceParam ? parseInt(minPriceParam) : 0,
    maxPriceParam ? parseInt(maxPriceParam) : 100000,
  ]);

  // Filter state
  const [filters, setFilters] = useState<FilterState>({
    category: categoryParam || "all",
    minPrice: minPriceParam ? parseInt(minPriceParam) : 0,
    maxPrice: maxPriceParam ? parseInt(maxPriceParam) : 100000,
    available: availableParam ? availableParam === "true" : null,
    discount: discountParam ? discountParam === "true" : null,
    sort: sortParam || "default",
  });

  // Track if filters have been changed
  const [filterChanged, setFilterChanged] = useState(false);

  // Debounce filter changes
  const debouncedFilters = useDebounce(filters, 1000);

  // Load products and extract categories
  useEffect(() => {
    const fetchProducts = async () => {
      setIsLoading(true);
      try {
        const allProducts = await getProducts();
        setProducts(allProducts);

        // Extract unique categories
        const uniqueCategories = Array.from(
          new Set(allProducts.map((product) => product.category))
        );
        setCategories(uniqueCategories);

        // Set price range based on min and max prices in products
        if (allProducts.length > 0) {
          const prices = allProducts.map((product) => product.price);
          const minPrice = Math.floor(Math.min(...prices));
          const maxPrice = Math.ceil(Math.max(...prices));

          // Only update if not already set from URL params
          if (!minPriceParam && !maxPriceParam) {
            setPriceRange([minPrice, maxPrice]);
            setFilters((prev) => ({
              ...prev,
              minPrice,
              maxPrice,
            }));
          }
        }
      } catch (error) {
        console.error("Error fetching products:", error);
        // Handle error state if needed
      } finally {
        setIsLoading(false);
      }
    };

    fetchProducts();
  }, [minPriceParam, maxPriceParam]);

  // Apply filters when debouncedFilters changes
  useEffect(() => {
    if (products.length > 0) {
      let filtered = [...products];

      // Apply category filter
      if (filters.category && filters.category.toLowerCase() !== "all") {
        filtered = filtered.filter(
          (product) =>
            product.category.toLowerCase() === filters.category.toLowerCase()
        );
      }

      // Apply search filter
      if (searchQuery) {
        const query = searchQuery.toLowerCase();
        filtered = filtered.filter(
          (product) =>
            product.title.toLowerCase().includes(query) ||
            product.category.toLowerCase().includes(query) ||
            (product.description &&
              product.description.toLowerCase().includes(query))
        );
      }

      // Apply price range filter
      filtered = filtered.filter(
        (product) =>
          product.price >= filters.minPrice && product.price <= filters.maxPrice
      );

      // Apply availability filter
      if (filters.available !== null) {
        filtered = filtered.filter(
          (product) => product.available === filters.available
        );
      }

      // Apply discount filter
      if (filters.discount !== null) {
        filtered = filtered.filter((product) =>
          filters.discount
            ? parseInt(product.discount) > 0
            : parseInt(product.discount) === 0
        );
      }

      // Apply sorting
      if (filters.sort !== "default") {
        switch (filters.sort) {
          case "price-low-high":
            filtered.sort((a, b) => a.price - b.price);
            break;
          case "price-high-low":
            filtered.sort((a, b) => b.price - a.price);
            break;
          case "newest":
            filtered.sort((a, b) => {
              const dateA = a.created ? new Date(a.created).getTime() : 0;
              const dateB = b.created ? new Date(b.created).getTime() : 0;
              return dateB - dateA;
            });
            break;
          case "discount":
            filtered.sort(
              (a, b) => parseInt(b.discount) - parseInt(a.discount)
            );
            break;
        }
      }

      setFilteredProducts(filtered);
    }
  }, [
    debouncedFilters,
    searchQuery,
    products,
    filters.category,
    filters.minPrice,
    filters.maxPrice,
    filters.available,
    filters.discount,
    filters.sort,
  ]);

  // Update selected category and search query when URL parameters change
  useEffect(() => {
    if (categoryParam) {
      setSelectedCategory(categoryParam.toLowerCase());
      setFilters((prev) => ({
        ...prev,
        category: categoryParam.toLowerCase(),
      }));
    }
    if (queryParam) {
      setSearchQuery(queryParam);
    }
    if (minPriceParam) {
      setFilters((prev) => ({ ...prev, minPrice: parseInt(minPriceParam) }));
    }
    if (maxPriceParam) {
      setFilters((prev) => ({ ...prev, maxPrice: parseInt(maxPriceParam) }));
    }
    if (availableParam) {
      setFilters((prev) => ({ ...prev, available: availableParam === "true" }));
    }
    if (discountParam) {
      setFilters((prev) => ({ ...prev, discount: discountParam === "true" }));
    }
    if (sortParam) {
      setFilters((prev) => ({ ...prev, sort: sortParam }));
    }
  }, [
    categoryParam,
    queryParam,
    minPriceParam,
    maxPriceParam,
    availableParam,
    discountParam,
    sortParam,
  ]);

  // Helper function to format currency
  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat("en-US", {
      style: "currency",
      currency: "PKR",
      minimumFractionDigits: 0,
      maximumFractionDigits: 0,
    }).format(amount);
  };

  // Update URL with current filters - using useCallback to avoid dependency issues
  const updateUrlWithFilters = useCallback(
    (overrides: Partial<{ query: string } & FilterState> = {}) => {
      const params = new URLSearchParams();

      // Add search query if present
      const query =
        overrides.query !== undefined ? overrides.query : searchQuery;
      if (query) {
        params.append("query", query);
      }

      // Add category if not "all"
      const category =
        overrides.category !== undefined
          ? overrides.category
          : filters.category;
      if (category && category !== "all") {
        params.append("category", category);
      }

      // Add price range if not default
      const minPrice =
        overrides.minPrice !== undefined
          ? overrides.minPrice
          : filters.minPrice;
      const maxPrice =
        overrides.maxPrice !== undefined
          ? overrides.maxPrice
          : filters.maxPrice;
      if (minPrice !== priceRange[0]) {
        params.append("minPrice", minPrice.toString());
      }
      if (maxPrice !== priceRange[1]) {
        params.append("maxPrice", maxPrice.toString());
      }

      // Add availability if set
      const available =
        overrides.available !== undefined
          ? overrides.available
          : filters.available;
      if (available !== null) {
        params.append("available", available.toString());
      }

      // Add discount if set
      const discount =
        overrides.discount !== undefined
          ? overrides.discount
          : filters.discount;
      if (discount !== null) {
        params.append("discount", discount.toString());
      }

      // Add sort if not default
      const sort = overrides.sort !== undefined ? overrides.sort : filters.sort;
      if (sort !== "default") {
        params.append("sort", sort);
      }

      // Update URL
      const url = params.toString()
        ? `/products?${params.toString()}`
        : "/products";
      router.push(url);
    },
    [searchQuery, filters, priceRange, router]
  );

  // Update URL when filters change
  useEffect(() => {
    if (!isLoading && filterChanged) {
      updateUrlWithFilters();
    }
  }, [debouncedFilters, isLoading, filterChanged, updateUrlWithFilters]);

  // Handle search submission
  const handleSearch = (query: string) => {
    setSearchQuery(query);

    // Update URL with new search query while preserving filters
    updateUrlWithFilters({ query });
  };

  // Handle filter changes
  const handleFilterChange = useCallback(
    (
      filterName: keyof FilterState,
      value: string | number | boolean | null
    ) => {
      setFilters((prev) => {
        const newFilters = { ...prev, [filterName]: value };

        // If changing category, also update selectedCategory for backward compatibility
        if (filterName === "category") {
          setSelectedCategory(value as string);
        }

        // Mark filters as changed
        setFilterChanged(true);

        // Update URL with new filters after debounce
        return newFilters;
      });
    },
    [setSelectedCategory]
  );

  // Clear all filters
  const handleClearFilters = useCallback(() => {
    // Reset to default values
    setFilters({
      category: "all",
      minPrice: priceRange[0],
      maxPrice: priceRange[1],
      available: null,
      discount: null,
      sort: "default",
    });

    // Reset selected category
    setSelectedCategory("all");

    // Reset filter changed flag
    setFilterChanged(false);

    // Update URL
    router.push("/products");
  }, [priceRange, router, setSelectedCategory, setFilters, setFilterChanged]);

  if (isLoading) {
    return (
      <div className="container mx-auto px-4 py-16 flex justify-center items-center">
        <div className="animate-pulse">Loading products...</div>
      </div>
    );
  }

  return (
    <div className="container mx-auto px-4 py-8">
      <div className="mb-6">
        <h1 className="text-3xl font-bold mb-2">Our Products</h1>
        <p className="text-gray-600">
          Discover our collection of handcrafted Chinioti wooden furniture
        </p>
      </div>

      {/* Search bar */}
      <div className="mb-6">
        <div className="flex flex-col sm:flex-row gap-4">
          <div className="flex-grow">
            <SearchField
              fullWidth
              placeholder="Search for products..."
              onSearch={handleSearch}
              initialQuery={searchQuery}
              debounceTime={300}
              autoSubmit={true}
              products={products}
              showSuggestions={true}
              maxSuggestions={5}
              onSelectSuggestion={(product) => {
                router.push(`/products/${product.id}`);
              }}
            />
          </div>

          {/* Mobile filter toggle */}
          <div className="md:hidden">
            <Button
              variant="outline"
              className="w-full flex items-center justify-center gap-2"
              onClick={() => setShowFilters(!showFilters)}
            >
              <SlidersHorizontal size={16} />
              <span>{showFilters ? "Hide Filters" : "Show Filters"}</span>
            </Button>
          </div>
        </div>

        {/* Search results info */}
        {searchQuery && (
          <div className="mt-3 text-sm text-gray-600">
            <p>
              Showing results for{" "}
              <span className="font-medium">&quot;{searchQuery}&quot;</span>
              {selectedCategory !== "all" && (
                <>
                  {" "}
                  in <span className="font-medium">{selectedCategory}</span>
                </>
              )}
            </p>
          </div>
        )}
      </div>

      <div className="flex flex-col md:flex-row gap-8">
        {/* Filters sidebar */}
        <motion.div
          className={`md:w-64 flex-shrink-0 ${
            showFilters ? "block" : "hidden md:block"
          }`}
          initial={{ opacity: 0, x: -20 }}
          animate={{ opacity: 1, x: 0 }}
          transition={{ duration: 0.3 }}
        >
          <div className="bg-white p-4 rounded-lg shadow-sm border border-gray-100">
            {/* Filter header with clear button */}
            <div className="flex items-center justify-between mb-6">
              <div className="flex items-center gap-2">
                <Filter size={18} className="text-gray-500" />
                <h2 className="font-medium">Filters</h2>
              </div>

              {filterChanged && (
                <Button
                  variant="ghost"
                  size="sm"
                  className="text-xs flex items-center gap-1 text-gray-500 hover:text-accent"
                  onClick={handleClearFilters}
                >
                  <RefreshCw size={12} />
                  Reset
                </Button>
              )}
            </div>

            {/* Categories section */}
            <div className="mb-6">
              <h3 className="text-sm font-medium mb-3">Categories</h3>
              <div className="space-y-2">
                <button
                  className={`w-full text-left px-3 py-2 rounded-md transition-colors ${
                    filters.category === "all"
                      ? "bg-accent text-white"
                      : "hover:bg-gray-100"
                  }`}
                  onClick={() => handleFilterChange("category", "all")}
                >
                  All Products
                </button>

                {categories.map((category, idx) => (
                  <button
                    key={idx}
                    className={`w-full text-left px-3 py-2 rounded-md transition-colors ${
                      filters.category === category.toLowerCase()
                        ? "bg-accent text-white"
                        : "hover:bg-gray-100"
                    }`}
                    onClick={() =>
                      handleFilterChange("category", category.toLowerCase())
                    }
                  >
                    {category}
                  </button>
                ))}
              </div>
            </div>

            {/* Price range section */}
            <div className="mb-6">
              <h3 className="text-sm font-medium mb-3">Price Range</h3>
              <div className="px-2">
                <div className="flex justify-between text-xs text-gray-500 mb-2">
                  <span>{formatCurrency(filters.minPrice)}</span>
                  <span>{formatCurrency(filters.maxPrice)}</span>
                </div>
                <input
                  type="range"
                  min={priceRange[0]}
                  max={priceRange[1]}
                  value={filters.minPrice}
                  onChange={(e) =>
                    handleFilterChange("minPrice", parseInt(e.target.value))
                  }
                  className="w-full accent-accent mb-2"
                />
                <input
                  type="range"
                  min={priceRange[0]}
                  max={priceRange[1]}
                  value={filters.maxPrice}
                  onChange={(e) =>
                    handleFilterChange("maxPrice", parseInt(e.target.value))
                  }
                  className="w-full accent-accent"
                />
              </div>
            </div>

            {/* Availability section */}
            <div className="mb-6">
              <h3 className="text-sm font-medium mb-3">Availability</h3>
              <div className="space-y-2">
                <button
                  className={`w-full text-left px-3 py-2 rounded-md transition-colors flex items-center gap-2 ${
                    filters.available === null
                      ? "bg-accent text-white"
                      : "hover:bg-gray-100"
                  }`}
                  onClick={() => handleFilterChange("available", null)}
                >
                  <span>All</span>
                </button>
                <button
                  className={`w-full text-left px-3 py-2 rounded-md transition-colors flex items-center gap-2 ${
                    filters.available === true
                      ? "bg-accent text-white"
                      : "hover:bg-gray-100"
                  }`}
                  onClick={() => handleFilterChange("available", true)}
                >
                  <CheckCircle
                    size={16}
                    className={
                      filters.available === true
                        ? "text-white"
                        : "text-green-500"
                    }
                  />
                  <span>In Stock</span>
                </button>
                <button
                  className={`w-full text-left px-3 py-2 rounded-md transition-colors flex items-center gap-2 ${
                    filters.available === false
                      ? "bg-accent text-white"
                      : "hover:bg-gray-100"
                  }`}
                  onClick={() => handleFilterChange("available", false)}
                >
                  <X
                    size={16}
                    className={
                      filters.available === false
                        ? "text-white"
                        : "text-red-500"
                    }
                  />
                  <span>Out of Stock</span>
                </button>
              </div>
            </div>

            {/* Discount section */}
            <div className="mb-6">
              <h3 className="text-sm font-medium mb-3">Discount</h3>
              <div className="space-y-2">
                <button
                  className={`w-full text-left px-3 py-2 rounded-md transition-colors ${
                    filters.discount === null
                      ? "bg-accent text-white"
                      : "hover:bg-gray-100"
                  }`}
                  onClick={() => handleFilterChange("discount", null)}
                >
                  All
                </button>
                <button
                  className={`w-full text-left px-3 py-2 rounded-md transition-colors flex items-center gap-2 ${
                    filters.discount === true
                      ? "bg-accent text-white"
                      : "hover:bg-gray-100"
                  }`}
                  onClick={() => handleFilterChange("discount", true)}
                >
                  <Percent
                    size={16}
                    className={
                      filters.discount === true ? "text-white" : "text-accent"
                    }
                  />
                  <span>On Sale</span>
                </button>
                <button
                  className={`w-full text-left px-3 py-2 rounded-md transition-colors ${
                    filters.discount === false
                      ? "bg-accent text-white"
                      : "hover:bg-gray-100"
                  }`}
                  onClick={() => handleFilterChange("discount", false)}
                >
                  Regular Price
                </button>
              </div>
            </div>

            {/* Sort section */}
            <div className="mb-4">
              <h3 className="text-sm font-medium mb-3">Sort By</h3>
              <div className="space-y-2">
                <button
                  className={`w-full text-left px-3 py-2 rounded-md transition-colors ${
                    filters.sort === "default"
                      ? "bg-accent text-white"
                      : "hover:bg-gray-100"
                  }`}
                  onClick={() => handleFilterChange("sort", "default")}
                >
                  Default
                </button>
                <button
                  className={`w-full text-left px-3 py-2 rounded-md transition-colors ${
                    filters.sort === "price-low-high"
                      ? "bg-accent text-white"
                      : "hover:bg-gray-100"
                  }`}
                  onClick={() => handleFilterChange("sort", "price-low-high")}
                >
                  Price: Low to High
                </button>
                <button
                  className={`w-full text-left px-3 py-2 rounded-md transition-colors ${
                    filters.sort === "price-high-low"
                      ? "bg-accent text-white"
                      : "hover:bg-gray-100"
                  }`}
                  onClick={() => handleFilterChange("sort", "price-high-low")}
                >
                  Price: High to Low
                </button>
                <button
                  className={`w-full text-left px-3 py-2 rounded-md transition-colors ${
                    filters.sort === "newest"
                      ? "bg-accent text-white"
                      : "hover:bg-gray-100"
                  }`}
                  onClick={() => handleFilterChange("sort", "newest")}
                >
                  Newest First
                </button>
                <button
                  className={`w-full text-left px-3 py-2 rounded-md transition-colors ${
                    filters.sort === "discount"
                      ? "bg-accent text-white"
                      : "hover:bg-gray-100"
                  }`}
                  onClick={() => handleFilterChange("sort", "discount")}
                >
                  Biggest Discount
                </button>
              </div>
            </div>

            {/* Apply button for mobile */}
            <div className="md:hidden mt-4">
              <Button
                variant="default"
                className="w-full"
                onClick={() => setShowFilters(false)}
              >
                Apply Filters
              </Button>
            </div>
          </div>
        </motion.div>

        {/* Products grid */}
        <div className="flex-1">
          {filteredProducts.length === 0 ? (
            <div className="text-center py-16">
              <h3 className="text-xl font-medium mb-2">No products found</h3>
              <p className="text-gray-500">
                Try selecting a different category or check back later.
              </p>
            </div>
          ) : (
            <>
              <div className="flex justify-between items-center mb-6">
                <p className="text-gray-600">
                  {searchQuery
                    ? `Found ${filteredProducts.length} ${
                        filteredProducts.length === 1 ? "result" : "results"
                      }`
                    : `Showing ${filteredProducts.length} ${
                        filteredProducts.length === 1 ? "product" : "products"
                      }`}
                </p>
              </div>

              <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-3 gap-6">
                {filteredProducts.map((product, idx) => (
                  <ProductCard key={product.id} product={product} index={idx} />
                ))}
              </div>
            </>
          )}
        </div>
      </div>
    </div>
  );
};

export default Products;
